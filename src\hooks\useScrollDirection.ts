import { useEffect, useState } from "react";

type ScrollDirection = "up" | "down" | null;

interface UseScrollDirectionOptions {
  initialDirection?: ScrollDirection;
  thresholdPixels?: number;
  off?: boolean;
}

export const useScrollDirection = ({
  initialDirection = null,
  thresholdPixels = 10,
  off = false,
}: UseScrollDirectionOptions = {}) => {
  const [scrollDirection, setScrollDirection] =
    useState<ScrollDirection>(initialDirection);
  const [prevScrollY, setPrevScrollY] = useState<number>(0);

  useEffect(() => {
    const handleScroll = () => {
      if (off) return;

      const currentScrollY = window.scrollY;

      if (Math.abs(currentScrollY - prevScrollY) < thresholdPixels) {
        // Not enough scroll to trigger direction change
        return;
      }

      const newScrollDirection = currentScrollY > prevScrollY ? "down" : "up";

      if (newScrollDirection !== scrollDirection) {
        setScrollDirection(newScrollDirection);
      }

      setPrevScrollY(currentScrollY);
    };

    // Add passive event listener for better performance
    window.addEventListener("scroll", handleScroll, { passive: true });

    return () => window.removeEventListener("scroll", handleScroll);
  }, [scrollDirection, prevScrollY, thresholdPixels, off]);

  return scrollDirection;
};
