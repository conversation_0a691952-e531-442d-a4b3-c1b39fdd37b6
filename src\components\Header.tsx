import { motion } from "framer-motion";
import { useEffect, useRef, useState } from "react";
import { BsArrowRight } from "react-icons/bs";
import { FiMenu, FiX } from "react-icons/fi";
import { Link } from "react-scroll";
import { useScrollDirection } from "../hooks/useScrollDirection";

const Header = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [activeLink, setActiveLink] = useState("hero");
  const headerRef = useRef<HTMLElement>(null);

  // Only apply scroll direction behavior on mobile
  const scrollDirection = useScrollDirection({
    off: !isMobile || isOpen, // Disable when menu is open or on desktop
    thresholdPixels: 20,
  });

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  // Check if we're on mobile
  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768); // 768px is the md breakpoint in Tailwind
    };

    // Initial check
    checkIsMobile();

    // Add resize listener
    window.addEventListener("resize", checkIsMobile);
    return () => window.removeEventListener("resize", checkIsMobile);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    document.addEventListener("scroll", handleScroll);
    return () => {
      document.removeEventListener("scroll", handleScroll);
    };
  }, [scrolled]);

  // Handle active section for nav highlighting
  useEffect(() => {
    const handleScroll = () => {
      const sections = document.querySelectorAll("section[id]");
      const scrollPosition = window.scrollY + 100;

      sections.forEach((section) => {
        const sectionId = section.id;
        const sectionHeight = section.clientHeight;
        const sectionTop = (section as HTMLElement).offsetTop;

        if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
          setActiveLink(sectionId);
        }
      });
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <>
      <header
        ref={headerRef}
        className={`fixed left-0 top-0 z-50 w-full transition-all duration-300 ${
          isMobile
            ? "bg-gray-900/75 backdrop-blur-lg shadow-md" // Match mobile menu styling exactly
            : "bg-transparent"
        } ${isMobile && scrollDirection === "down" && scrolled && !isOpen ? "-translate-y-full" : "translate-y-0"}`}
      >
        <div className="container-custom flex h-20 items-center justify-center">
          {/* Desktop Navigation Pill */}
          {!isMobile && (
            <div className="relative h-12 flex items-center justify-center">
              {/* Base pill with nav links */}
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className={`inline-flex h-12 items-center rounded-md border border-gray-800 py-3 transition-all duration-300 ${
                  scrolled
                    ? "bg-gray-900/65 backdrop-blur-lg shadow-md" // More translucent background
                    : "bg-gray-900/55 backdrop-blur-md" // More translucent background
                }`}
                style={{
                  borderRadius: "8px",
                  boxShadow: scrolled ? "0 4px 15px rgba(0, 0, 0, 0.2), 0 0 10px rgba(158, 127, 247, 0.05)" : "none",
                  width: scrolled ? "auto" : "auto", // Allow width to adjust based on content
                  paddingLeft: "1.5rem",
                  paddingRight: scrolled ? "180px" : "1.5rem", // Add space for the CTA button when scrolled
                  pointerEvents: "auto" // Ensure nav pill is clickable
                }}
              >
                <nav className="flex justify-center items-center relative z-10">
                  <NavLinks desktop activeLink={activeLink} />
                </nav>
              </motion.div>

              {/* CTA Button that appears inside pill when scrolled */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{
                  opacity: scrolled ? 1 : 0,
                  x: scrolled ? 0 : 20
                }}
                transition={{
                  duration: 0.4,
                  ease: "easeInOut"
                }}
                className="absolute right-0 top-0 h-12 flex items-center"
                style={{
                  borderTopRightRadius: "8px",
                  borderBottomRightRadius: "8px",
                  overflow: "hidden",
                  pointerEvents: scrolled ? "auto" : "none",
                }}
              >
                <div className="bg-gray-900/65 backdrop-blur-lg h-full flex items-center pr-0.5 rounded-r-md">
                  <a
                    href="https://calendly.com/diftra/intro-call"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="btn-outline-gradient group flex items-center gap-2 px-4 py-1.5 font-medium transition-all duration-300 hover:-translate-y-1 hover:shadow-lg hover:shadow-purple-900/20 whitespace-nowrap h-[38px]"
                  >
                    <span>Work With Us</span>
                    <motion.span
                      className="inline-block"
                      animate={{ x: [0, 2, 0] }}
                      transition={{
                        duration: 1.5,
                        repeat: Number.POSITIVE_INFINITY,
                        repeatDelay: 4,
                      }}
                    >
                      <BsArrowRight className="transition-all duration-300 group-hover:translate-x-1" />
                    </motion.span>
                  </a>
                </div>
              </motion.div>
            </div>
          )}

          {/* Mobile Navigation Toggle */}
          <div className="md:hidden absolute right-4">
            <button
              onClick={toggleMenu}
              className="rounded-md p-2 text-gray-300 hover:bg-gray-800 hover:text-white"
              aria-label="Toggle menu"
            >
              {isOpen ? <FiX size={24} /> : <FiMenu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation Menu - Inside header with no background */}
        <motion.div
          initial={{ height: 0, opacity: 0 }}
          animate={{
            height: isOpen ? "auto" : 0,
            opacity: isOpen ? 1 : 0,
          }}
          transition={{
            duration: 0.3,
            ease: [0.25, 0.1, 0.25, 1.0], // Smooth easing function
          }}
          className="md:hidden overflow-hidden"
        >
          <div className="container-custom space-y-4 py-6 landscape:py-3 landscape:space-y-2">
            <NavLinks desktop={false} onClick={() => setIsOpen(false)} activeLink={activeLink} />
            <div className="pt-4 landscape:pt-1 landscape:w-40 landscape:mx-auto">
              <a
                href="https://calendly.com/diftra/intro-call"
                target="_blank"
                rel="noopener noreferrer"
                className="btn-outline-gradient group flex w-full items-center justify-center gap-2 px-4 py-3 font-medium landscape:py-2 text-center transition-all duration-300 hover:-translate-y-1"
              >
                <span>Work With Us</span>
                <motion.span
                  className="inline-block"
                  animate={{ x: [0, 2, 0] }}
                  transition={{
                    duration: 1.5,
                    repeat: Number.POSITIVE_INFINITY,
                    repeatDelay: 4,
                  }}
                >
                  <BsArrowRight className="transition-all duration-300 group-hover:translate-x-1" />
                </motion.span>
              </a>
            </div>
          </div>
        </motion.div>
      </header>
    </>
  );
};

interface NavLinksProps {
  desktop: boolean;
  onClick?: () => void;
  activeLink?: string;
}

const NavLinks = ({ desktop, onClick, activeLink = "" }: NavLinksProps) => {
  const links = [
    { to: "hero", label: "Home" },
    { to: "services", label: "Services" },
    { to: "why-choose-us", label: "Why Us" },
    { to: "process", label: "Process" },
    { to: "why-now", label: "Why Now" },
    { to: "faq", label: "FAQ" },
    { to: "contact", label: "Contact" },
  ];

  if (desktop) {
    return (
      <>
        {links.map((link) => (
          <Link
            key={link.to}
            to={link.to}
            spy={true}
            smooth={true}
            offset={-80}
            duration={800}
            onClick={onClick}
            className={`cursor-pointer font-medium transition-all duration-500 hover:text-purple-400 inline-block mx-3.5 ${
              activeLink === link.to ? "text-purple-400" : "text-gray-200"
            }`}
          >
            {link.label}
          </Link>
        ))}
      </>
    );
  }

  // Mobile layout
  return (
    <div className="portrait:block landscape:flex landscape:flex-wrap landscape:justify-between landscape:gap-1">
      {links.map((link) => (
        <Link
          key={link.to}
          to={link.to}
          spy={true}
          smooth={true}
          offset={-80}
          duration={800}
          onClick={onClick}
          className={`cursor-pointer font-medium transition-colors hover:text-purple-400 block py-2 landscape:py-1 landscape:text-sm landscape:px-2 ${
            activeLink === link.to ? "text-purple-400" : ""
          }`}
        >
          {link.label}
        </Link>
      ))}
    </div>
  );
};

export default Header;
