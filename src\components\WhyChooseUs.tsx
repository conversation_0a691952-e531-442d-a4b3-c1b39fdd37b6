import { motion } from "framer-motion";
import { FaWandMagicSparkles } from "react-icons/fa6";
import {
  FiA<PERSON>,
  FiCheckCircle,
  FiClock,
  FiThumbsUp,
  FiTrendingUp,
} from "react-icons/fi";

const WhyChooseUs = () => {
  const reasons = [
    {
      icon: <FiClock className="h-8 w-8" />,
      title: "Save Valuable Time",
      description:
        "Our automation ecosystems eliminate repetitive tasks and streamline your workflows, giving your team back hours each day to focus on strategy and creativity.",
    },
    {
      icon: <FiTrendingUp className="h-8 w-8" />,
      title: "Scale Your Production",
      description:
        "Increase your content output exponentially without adding headcount or compromising quality. Handle more clients with the same team size.",
    },
    {
      icon: <FiCheckCircle className="h-8 w-8" />,
      title: "Maintain Consistency",
      description:
        "Ensure consistent brand imaging and quality across all channels and campaigns with standardized processes and automated quality control.",
    },
    {
      icon: <FiAward className="h-8 w-8" />,
      title: "Stay Competitive",
      description:
        "In an increasingly competitive market, automation gives you the edge to deliver superior results faster and more efficiently than competitors.",
    },
    {
      icon: <FaWandMagicSparkles className="h-8 w-8" />,
      title: "Content on Autopilot",
      description:
        "Experience true automation with minimal oversight required. We manage the entire infrastucture and handle all the technical details so you can focus primarily on your client relationships and reviewing the final content.",
    },
    {
      icon: <FiThumbsUp className="h-8 w-8" />,
      title: "Guaranteed Satisfaction",
      description:
        "We stand by our 100% satisfaction guarantee. We'll continue refining your automation ecosystem until you are completely content with the results.",
    },
  ];

  return (
    <section id="why-choose-us" className="section-padding bg-gray-950">
      <div className="container-custom">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="mb-16 text-center"
        >
          <h2 className="section-title">
            Why Choose <span className="gradient-text">Diftra</span>
          </h2>
          <p className="section-subtitle">
            We don't just offer automation tools – we create complete ecosystems
            that transform your agency's operations
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {reasons.map((reason, index) => (
            <motion.div
              key={`reason-${index}`}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, margin: "-50px" }}
              transition={{
                duration: 0.5,
                delay: index * 0.1,
                ease: "easeOut",
              }}
              className="flex gap-6"
            >
              <div className="flex-shrink-0">
                <div className="flex h-16 w-16 items-center justify-center rounded-lg bg-purple-600/20 text-purple-400">
                  {reason.icon}
                </div>
              </div>
              <div className="flex-1">
                <h3 className="mb-3 text-xl font-bold">{reason.title}</h3>
                <p className="text-gray-400">{reason.description}</p>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Results Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="mt-20 rounded-lg border border-gray-800 bg-gray-900 p-8"
        >
          <h3 className="mb-8 text-center text-2xl font-bold">
            Real <span className="gradient-text">Results</span> for Agencies
            Like Yours
          </h3>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            <div className="flex flex-col items-center rounded-lg border border-gray-800 bg-gray-800/50 p-6 text-center">
              <div className="text-3xl font-bold text-purple-400">Immediate</div>
              <div className="mt-2 text-lg font-semibold">
                Productivity Gains
              </div>
              <div className="mt-2 text-sm text-gray-400">
                Our clients experience tangible productivity improvements within
                the first week of implementation.
              </div>
            </div>

            <div className="flex flex-col items-center rounded-lg border border-gray-800 bg-gray-800/50 p-6 text-center">
              <div className="text-3xl font-bold text-purple-400">3x More</div>
              <div className="mt-2 text-lg font-semibold">
                Multi-Channel Campaigns Managed
              </div>
              <div className="mt-2 text-sm text-gray-400">
                Handle complex, multi-platform campaigns with ease, ensuring
                consistent messaging and delivery without added overhead.
              </div>
            </div>

            <div className="flex flex-col items-center rounded-lg border border-gray-800 bg-gray-800/50 p-6 text-center">
              <div className="text-3xl font-bold text-purple-400">76% Faster</div>
              <div className="mt-2 text-lg font-semibold">
                New Client Onboarding
              </div>
              <div className="mt-2 text-sm text-gray-400">
                Our ecosystems codify your best practices, allowing you to
                onboard new clients and projects in a fraction of the time,
                eliminating manual setup and process reinvention.
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default WhyChooseUs;
