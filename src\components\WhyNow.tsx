import { motion } from "framer-motion";

const WhyNow = () => {
  return (
    <section id="why-now" className="section-padding bg-gray-950">
      <div className="container-custom">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="max-w-3xl mx-auto text-center"
        >
          <h2 className="section-title mb-6">
            Why Look at Content Automation{" "}
            <span className="gradient-text">Now?</span>
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Automation is quietly changing how digital agencies produce and
            deliver value. With rising demands and shrinking timelines, more
            agencies are turning to automation not for speed alone, but to open
            up time for strategy and creativity.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-left">
            <div className="bg-gray-800/40 p-6 rounded-lg">
              <div className="font-bold text-purple-400 mb-2">
                Industry Movement
              </div>
              <div className="text-gray-400 text-sm">
                Over half of digital agencies have adopted some form of content
                automation, freeing up their teams to focus on relationships and
                creative work.
              </div>
            </div>
            <div className="bg-gray-800/40 p-6 rounded-lg">
              <div className="font-bold text-purple-400 mb-2">
                Evolving Client Expectations
              </div>
              <div className="text-gray-400 text-sm">
                Clients now expect faster turnarounds and more sophisticated
                multi-channel campaigns—often without an increase in budget.
              </div>
            </div>
            <div className="bg-gray-800/40 p-6 rounded-lg">
              <div className="font-bold text-purple-400 mb-2">
                Adaptation Over Disruption
              </div>
              <div className="text-gray-400 text-sm">
                Exploring automation doesn’t have to mean overnight change. Many
                agencies start small and grow their systems over time,
                responding to what works.
              </div>
            </div>
          </div>
          <p className="mt-8 text-gray-400 text-base">
            If you’re asking yourself, “Is now the right time?”—know that the
            move toward automation is accelerating, but the timing and approach
            should suit your agency’s unique needs and readiness. We’re happy to
            share what we see working in the industry so you can plot your own
            path forward.
          </p>
        </motion.div>
      </div>
    </section>
  );
};

export default WhyNow;
